import React from 'react';
import { View, Text, StyleSheet, Button } from 'react-native';
import CodePush from 'react-native-code-push';

const EmbeddedComponent = (props) => {
  const { title, dataFromNative } = props;

  return (
    <View style={[styles.container, props.style]}>
      <Text style={styles.title}>{title || 'Embedded RN Component 9'}</Text>
      {dataFromNative && (
        <Text style={styles.data}>
          Data: {JSON.stringify(dataFromNative, null, 2)}
        </Text>
      )}
      <Button 
        title="Press me (RN)" 
        onPress={() => console.log('RN Button Pressed!')} 
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 10,
    borderColor: 'blue',
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  data: {
    marginBottom: 8,
  }
});

//EmbeddedComponent = codePush(EmbeddedComponent);

export default CodePush(EmbeddedComponent);