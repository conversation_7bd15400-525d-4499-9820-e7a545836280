# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx4096m
# When configured, <PERSON><PERSON><PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true
# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app"s APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true
# Automatically convert third-party libraries to use AndroidX
android.enableJetifier=true
# AGP 8.0 generates R classes for resources defined in the current module only.
android.nonTransitiveRClass=false
# AGP 8.0 generates R classes with non-final fields by default.
android.nonFinalResIds=false
# R8 full mode
android.enableR8.fullMode=false

# Enables mavenLocal() repository
USE_MAVEN_LOCAL=false

# Enable marco benchmark module
ENABLE_MARCO_BENCHMARK=false
# Not uninstall benchmark app
# android.injected.androidTest.leaveApksInstalledAfterRun=true

# Enable baselineprofile module
ENABLE_BASELINE_PROFILE=false

## react native
reactNativeArchitectures=armeabi-v7a,arm64-v8a
newArchEnabled=true
hermesEnabled=true
mobileModulePath=./mobile-module/

## codepush
compileSdkVersion=android-34
nodeModulesPath=../mobile-module/node_modules


