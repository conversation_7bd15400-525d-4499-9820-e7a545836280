buildscript {
    repositories {
        gradlePluginPortal()
        google()
        mavenCentral()
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url = uri("https://storage.googleapis.com/r8-releases/raw") }
    }
    dependencies {
        classpath("com.android.tools:r8:8.9.31")
        classpath 'com.android.tools.build:gradle:8.9.0'
        classpath 'com.google.gms:google-services:4.4.2'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:3.0.3'
        classpath 'com.google.firebase:perf-plugin:1.4.2'
        classpath 'org.sonarsource.scanner.gradle:sonarqube-gradle-plugin:4.4.1.3373'

        classpath 'com.facebook.react:react-native-gradle-plugin'


        // Performance
        if (ENABLE_MARCO_BENCHMARK) {
            classpath 'org.jetbrains.kotlin:kotlin-gradle-plugin:1.9.25'
            classpath 'androidx.benchmark:benchmark-baseline-profile-gradle-plugin:1.3.0'
            classpath 'androidx.baselineprofile:androidx.baselineprofile.gradle.plugin:1.3.0'
        }
    }
}

allprojects {

    repositories {

        google()

        mavenCentral()

        maven { url 'https://maven.aliyun.com/repository/public' }

        if (USE_MAVEN_LOCAL.toBoolean()) {
            println('use mavenLocal build')
            mavenLocal()
        }

        maven { url "https://maven.sayweee.net/repository/maven-releases/" }

        maven { url "https://maven.sayweee.net/repository/maven-snapshots/" }

        maven { url "https://jitpack.io" }

        maven { url 'https://devrepo.kakao.com/nexus/content/groups/public/' }

    }

}

tasks.register('clean', Delete) {
    delete rootProject.buildDir
}

tasks.register("r8Version") {
    doLast {
        println(com.android.tools.r8.Version.getVersionString())
    }
}

apply plugin: "com.facebook.react.rootproject"

