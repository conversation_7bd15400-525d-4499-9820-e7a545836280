dependencies {
    implementation files('libs/platform_sdk_v2.4.0210.jar')
    implementation files('libs/dom4j-1.6.1.jar')
    implementation files('libs/WafMobileSdk.aar')

    /*implementation 'androidx.appcompat:appcompat:1.3.1'
    implementation 'com.google.android.material:material:1.5.0'*/
    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.2.1'
    implementation 'androidx.constraintlayout:constraintlayout-core:1.1.1'
    /*viewpager2*/
    implementation 'androidx.viewpager2:viewpager2:1.1.0'
    implementation 'androidx.coordinatorlayout:coordinatorlayout:1.3.0'

    // startup
    implementation "androidx.startup:startup-runtime:1.2.0"

    //facebook
    implementation 'com.facebook.android:facebook-share:16.0.1'
    /*appsflyer*/
    implementation 'com.appsflyer:af-android-sdk:6.15.2'
    /*firebase*/
    implementation(platform("com.google.firebase:firebase-bom:33.10.0"))
    implementation 'com.google.firebase:firebase-messaging'
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.firebase:firebase-crashlytics'
    implementation 'com.google.firebase:firebase-perf'
    /*auth*/
    implementation 'com.google.android.gms:play-services-auth:21.3.0'
    /*google map places*/
    implementation 'com.google.android.libraries.places:places:4.1.0'

    // play core
    // https://developer.android.com/guide/playcore
    // implementation 'com.google.android.play:core:1.10.3' // deprecated
    implementation 'com.google.android.play:review:2.0.2'

    //Urban Airship SDK
    implementation "com.urbanairship.android:urbanairship-fcm:17.2.0"

    //app扫一扫
    implementation 'com.github.bingoogolapple.BGAQRCode-Android:zxing:1.3.8'
    //热门搜索 流氏布局
    implementation 'com.hyman:flowlayout-lib:1.1.2'
    //leak canary 仅debug模式生效
    debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.14'
    /*adapter*/
    //noinspection GradleDependency
    implementation 'com.github.CymChad:BaseRecyclerViewAdapterHelper:2.9.49-androidx'
    /*status bar*/
    implementation 'com.gyf.immersionbar:immersionbar:3.0.0'

    // implementation 'com.github.bumptech.glide:glide:4.12.0'
    // implementation 'com.github.bumptech.glide:annotations:4.12.0'
    // annotationProcessor 'com.github.bumptech.glide:compiler:4.12.0'
    implementation 'com.github.bumptech.glide:glide:4.16.0'

    implementation 'com.squareup.retrofit2:retrofit:2.11.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.11.0'
    implementation 'com.squareup.retrofit2:adapter-rxjava2:2.11.0'
    implementation(platform("com.squareup.okhttp3:okhttp-bom:4.12.0"))
    implementation(platform("com.squareup.retrofit2:retrofit-bom:2.11.0"))

    implementation 'jp.wasabeef:blurry:4.0.1'

    /*动画支持*/
    implementation('com.airbnb.android:lottie:6.1.0') {
        transitive false
    }
    /*jwt*/
    implementation 'com.auth0.android:jwtdecode:2.0.0'
    /*refresh*/
    implementation 'com.scwang.smart:refresh-layout-kernel:2.0.3'
    implementation 'com.scwang.smart:refresh-header-classics:2.0.3'
    implementation 'com.scwang.smart:refresh-footer-classics:2.0.3'
    /*banner*/
    implementation 'com.bigkoo:convenientbanner:2.1.5'
    implementation 'io.github.youth5201314:banner:2.2.2'
    /*photo view*/
    implementation 'com.github.chrisbanes:PhotoView:2.3.0'
    /*guide*/
    implementation 'com.github.huburt-Hu:NewbieGuide:v2.4.4'
    /*扫描银行卡*/
    implementation 'io.card:android-sdk:5.5.1'

    implementation 'com.github.yellowcath:VideoProcessor:2.4.2'
    implementation 'com.yanzhenjie.recyclerview:x:1.3.2'
    /*地址自动补全*/
    //noinspection GradleDependency
    implementation('com.smartystreets.api:smartystreets-java-sdk:3.10.7') {
        exclude group: 'org.apache.httpcomponents', module: 'httpcore'
        exclude group: 'org.apache.httpcomponents', module: 'httpclient'
    }

    /*implementation 'io.sentry:sentry-android:6.3.1'*/
    // tooltip
    implementation "com.github.skydoves:balloon:1.4.7"

    implementation "com.kakao.sdk:v2-user:2.11.2" // Kakao Login
    implementation "com.kakao.sdk:v2-share:2.11.2" // Kakao Talk sharing

    // Wechat
    implementation 'com.tencent.mm.opensdk:wechat-sdk-android-without-mta:6.8.0'

    implementation 'com.linecorp.linesdk:linesdk:5.8.1'

    implementation 'com.github.klaviyo.klaviyo-android-sdk:analytics:2.4.1'
    implementation 'com.github.klaviyo.klaviyo-android-sdk:push-fcm:2.4.1'

    // youtube player
    // https://github.com/PierfrancescoSoffritti/android-youtube-player
    implementation 'com.pierfrancescosoffritti.androidyoutubeplayer:core:12.1.1'

    // MMKV
    // https://github.com/Tencent/MMKV
    implementation 'com.tencent:mmkv:1.3.14'
    implementation 'com.github.Othershe:CalendarView:1.2.1'

    implementation("com.facebook.react:react-android")
    implementation("com.facebook.react:hermes-android")
}

apply from: 'depends_internal.gradle'