package com.sayweee.weee.global;

import android.app.Application;
import android.content.Context;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.ViewModelStore;
import androidx.lifecycle.ViewModelStoreOwner;

import com.facebook.react.PackageList;
import com.facebook.react.ReactApplication;
import com.facebook.react.ReactHost;
import com.facebook.react.ReactNativeHost;
import com.facebook.react.ReactPackage;
import com.facebook.react.defaults.DefaultReactHost;
import com.facebook.react.defaults.DefaultReactNativeHost;
import com.microsoft.codepush.react.CodePush;
import com.sayweee.core.order.OrderProvider;
import com.sayweee.monitor.WeeeMonitor;
import com.sayweee.service.ContextService;
import com.sayweee.weee.BuildConfig;
import com.sayweee.weee.global.config.AppConfig;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.module.MainActivity;
import com.sayweee.weee.module.debug.log.LogFileManager;
import com.sayweee.weee.module.launch.service.AppIconManager;
import com.sayweee.weee.module.launch.service.SessionTokenHelper;
import com.sayweee.weee.module.navigate.NavigateHelper;
import com.sayweee.weee.react.ReactMainHelper;
import com.sayweee.weee.service.hotfix.WebViewHotfix;
import com.sayweee.weee.service.location.GeoInfoManager;
import com.sayweee.weee.widget.refresh.LoadingIndicatorRefreshHeader;
import com.sayweee.wrapper.helper.lang.LanguageProvider;
import com.sayweee.wrapper.helper.lifecycle.LifecycleCallbacksIml;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.scwang.smart.refresh.layout.api.RefreshHeader;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.DefaultRefreshHeaderCreator;

import java.util.List;

/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2020/9/29.
 * Desc:
 */
public class App extends Application implements ViewModelStoreOwner, ReactApplication {

    public static Application application;
    private ViewModelStore appViewModelStore;

    static {
        SmartRefreshLayout.setDefaultRefreshHeaderCreator(new DefaultRefreshHeaderCreator() {
            @NonNull
            @Override
            public RefreshHeader createRefreshHeader(@NonNull Context context, @NonNull RefreshLayout layout) {
                return new LoadingIndicatorRefreshHeader(context);
            }
        });
    }

    @Override
    public void onCreate() {
        super.onCreate();
        application = this;
        appViewModelStore = new ViewModelStore();
        AppConfig.attach(this);
        setLifecycleCallbacks();

        ReactMainHelper.loadReactNative(this);
    }

    @Override
    protected void attachBaseContext(Context base) {
        WeeeMonitor.getInstance().initAppStart();
        super.attachBaseContext(LanguageProvider.get().attachBaseContext(base));
        WebViewHotfix.setWebDataSuffixFix(application);
    }

    @NonNull
    @Override
    public ViewModelStore getViewModelStore() {
        return appViewModelStore;
    }

    private void setLifecycleCallbacks() {
        LifecycleProvider.get().registerLifeEventCallback(this, new LifecycleCallbacksIml.SimpleLifecycleEventCallback() {

            @Override
            public void onForeground() {
                super.onForeground();
                if (LifecycleProvider.get().isActivityAlive(MainActivity.class.getName())) {
                    GeoInfoManager.get().execLocateRefresh();
                    //延迟500ms，有些手机处于后台时会无网络
                    OrderProvider.get().refreshSimplePreOrderDelay(500L, false);
                    SessionTokenHelper.execSessionCheckDelay();
                    NavigateHelper.refreshNavigateLabel(1000);
                    // log retrieval
                    LogFileManager.get().uploadByRetrieval(3000);
                    ContextService.get().readyWafToken();
                }
            }

            @Override
            public void onBackground() {
                super.onBackground();
                AccountManager.get().updateSessionTokenValidityPeriod();
                AppIconManager.get().upgradeIconStyle();
            }
        });
    }

    @NonNull
    @Override
    public ReactNativeHost getReactNativeHost() {
//        return ReactMainHelper.reactNativeHost(this);
        return new DefaultReactNativeHost(this) {
            @Override
            public boolean getUseDeveloperSupport() {
                return BuildConfig.DEBUG;
            }

            @Override
            protected List<ReactPackage> getPackages() {
                return new PackageList(this).getPackages();
            }

            @Override
            protected String getJSMainModuleName() {
                return "index";
            }

            @Override
            protected boolean isNewArchEnabled() {
                return BuildConfig.IS_NEW_ARCHITECTURE_ENABLED;
            }

            @Nullable
            @Override
            protected Boolean isHermesEnabled() {
                return BuildConfig.IS_HERMES_ENABLED;
            }

            @Override
            protected String getJSBundleFile() {
//                return super.getJSBundleFile();
                return CodePush.getJSBundleFile();
            }
        };
    }

    @Nullable
    @Override
    public ReactHost getReactHost() {
        return DefaultReactHost.getDefaultReactHost(getApplicationContext(),
                getReactNativeHost(), null);
    }
}
