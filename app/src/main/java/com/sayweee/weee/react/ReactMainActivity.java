package com.sayweee.weee.react;

import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.facebook.react.ReactActivity;
import com.facebook.react.ReactActivityDelegate;
import com.facebook.react.ReactInstanceManager;
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint;
import com.facebook.react.defaults.DefaultReactActivityDelegate;
import com.microsoft.codepush.react.CodePush;
import com.microsoft.codepush.react.ReactInstanceHolder;

//
// Created by <PERSON><PERSON> on 18/06/2025.
//
public class ReactMainActivity extends ReactActivity {

    @Nullable
    @Override
    protected String getMainComponentName() {
        return "EmbeddedModule";
    }

    private ReactActivityDelegate mReactActivityDelegate;

    @Override
    protected ReactActivityDelegate createReactActivityDelegate() {
        if (mReactActivityDelegate == null) {
            mReactActivityDelegate = new DefaultReactActivityDelegate(
                    this,
                    getMainComponentName(),
                    DefaultNewArchitectureEntryPoint.getFabricEnabled());
        }
        return mReactActivityDelegate;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mReactActivityDelegate != null) {
            mReactActivityDelegate.onDestroy();
            mReactActivityDelegate = null;
        }
    }
}
