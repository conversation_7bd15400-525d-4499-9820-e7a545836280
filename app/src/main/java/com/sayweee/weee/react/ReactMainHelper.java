package com.sayweee.weee.react;


import android.app.Application;
import android.content.Context;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.facebook.react.PackageList;
import com.facebook.react.ReactApplication;
import com.facebook.react.ReactPackage;
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint;
import com.facebook.react.defaults.DefaultReactNativeHost;
import com.facebook.react.soloader.OpenSourceMergedSoMapping;
import com.facebook.soloader.SoLoader;
import com.microsoft.codepush.react.CodePush;
import com.sayweee.weee.BuildConfig;

import java.io.IOException;
import java.util.List;

//
// Created by <PERSON><PERSON> on 18/06/2025.
//
public class ReactMainHelper {

    public static DefaultReactNativeHost reactNativeHost(Application context) {
        return new DefaultReactNativeHost(context) {
            @Override
            public boolean getUseDeveloperSupport() {
                return BuildConfig.DEBUG;
            }

            @Override
            protected List<ReactPackage> getPackages() {
                return new PackageList(context).getPackages();
            }

            @Override
            protected String getJSMainModuleName() {
                return "index";
            }

            @Override
            protected boolean isNewArchEnabled() {
                return BuildConfig.IS_NEW_ARCHITECTURE_ENABLED;
            }

            @Nullable
            @Override
            protected Boolean isHermesEnabled() {
                return BuildConfig.IS_HERMES_ENABLED;
            }

            @Override
            protected String getJSBundleFile() {
//                return super.getJSBundleFile();
                return CodePush.getJSBundleFile();
            }
        };
    }

    public static void loadReactNative(Context context) {
        try {
            SoLoader.init(context, OpenSourceMergedSoMapping.INSTANCE);
        } catch (IOException error) {
            throw new RuntimeException(error);
        }

        if (BuildConfig.IS_NEW_ARCHITECTURE_ENABLED) {
            DefaultNewArchitectureEntryPoint.load();
        }

//        if (context instanceof ReactApplication) {
//            ((ReactApplication) context).getReactNativeHost().getReactInstanceManager().createReactContextInBackground();
//        }
    }
}
