apply plugin: 'com.android.application'
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'
apply plugin: 'com.google.firebase.firebase-perf'
apply plugin: "com.facebook.react"
apply from: "../../reactlib/node_modules/@bravemobile/react-native-code-push/android/codepush.gradle"

//project.ext.react = [
//        nodeModulesPath: file('../reactlib/node_modules').absolutePath
//]

react {
    /* Folders */
    //   The root of your project, i.e. where "package.json" lives. Default is '../..'
     root = file("../../reactlib")
    //   The folder where the react-native NPM package is. Default is ../../node_modules/react-native
     reactNativeDir = file("../../reactlib/node_modules/react-native")
    //   The folder where the react-native Codegen package is. Default is ../../node_modules/@react-native/codegen
     codegenDir = file("../../reactlib/node_modules/@react-native/codegen")
    //   The cli.js file which is the React Native CLI entrypoint. Default is ../../node_modules/react-native/cli.js
     cliFile = file("../../reactlib/node_modules/react-native/cli.js")

    /* Variants */
    //   The list of variants to that are debuggable. For those we're going to
    //   skip the bundling of the JS bundle and the assets. By default is just 'debug'.
    //   If you add flavors like lite, prod, etc. you'll have to list your debuggableVariants.
    debuggableVariants = ["officialDebug"]

    /* Bundling */
    //   A list containing the node command and its flags. Default is just 'node'.
    // nodeExecutableAndArgs = ["node"]
    //
    //   The command to run when bundling. By default is 'bundle'
    // bundleCommand = "ram-bundle"
    //
    //   The path to the CLI configuration file. Default is empty.
    // bundleConfig = file(../rn-cli.config.js)
    //
    //   The name of the generated asset file containing your JS bundle
    // bundleAssetName = "MyApplication.android.bundle"
    //
    //   The entry file for bundle generation. Default is 'index.android.js' or 'index.js'
    // entryFile = file("../js/MyApplication.android.js")
    //
    //   A list of extra flags to pass to the 'bundle' commands.
    //   See https://github.com/react-native-community/cli/blob/main/docs/commands.md#bundle
    // extraPackagerArgs = []

    /* Hermes Commands */
    //   The hermes compiler command to run. By default it is 'hermesc'
    // hermesCommand = "$rootDir/my-custom-hermesc/bin/hermesc"
    //
    //   The list of flags to pass to the Hermes compiler. By default is "-O", "-output-source-map"
    // hermesFlags = ["-O", "-output-source-map"]

//    autolinkInputFile = "../../reactlib/android/build/generated/autolinking/autolinking.json"
    /* Autolinking */
    autolinkLibrariesWithApp()
}

apply from: 'version.gradle'

android {

    namespace 'com.sayweee.weee'

    compileSdk androidCompileSdk

    defaultConfig {
        applicationId "com.sayweee.weee"
        minSdk androidMinSdk
        targetSdk androidTargetSdk
        versionCode appVersionCode
        versionName appVersionName
        multiDexEnabled true
        buildConfigField("String", "BUILD_TIME", "\"\"")

        manifestPlaceholders = [
                pkgName: 'com.sayweee.weee'
        ]
    }

    signingConfigs {
        config {
            keyAlias 'Weee'
            keyPassword '1q2w3e4r'
            storeFile file('./weee.keystore')
            storePassword '1q2w3e4r'

            v1SigningEnabled true
            v2SigningEnabled true
        }
    }

    buildTypes {
        debug {
            debuggable true
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.config

            ndk {
                abiFilters 'armeabi-v7a', 'arm64-v8a', 'x86', 'x86_64'
            }

            firebaseCrashlytics {
                mappingFileUploadEnabled false
            }
        }

        release {
            debuggable false
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.config
            buildConfigField("String", "BUILD_TIME", "\"" + new Date().format("yyyy-MM-dd HH:mm") + "\"")

            ndk {
                //noinspection ChromeOsAbiSupport
                abiFilters 'armeabi-v7a', 'arm64-v8a'
            }
        }
    }

    //It is forbidden to adjust the configuration here. If you use it locally, please do not submit it.
    applicationVariants.configureEach { variant ->
        if (variant.buildType.getName() == 'release') {
            def branchName = 'git rev-parse --abbrev-ref HEAD'.execute().text.trim().replace('/', '-')
            def dateTime = new Date().format("yyyyMMdd_HHmm");
            def suffix = new StringBuilder()
                    .append("_[" + branchName + "]_")
                    .append(dateTime)
                    .append("_v" + defaultConfig.versionName + "_" + defaultConfig.versionCode + "_" + buildType.name)
                    .append(".apk")
                    .toString()
            variant.outputs.configureEach {
                def pathname
                if (variant.name.startsWith("official_hw")) {
                    pathname = "Weee! Huawei " + suffix;
                } else if (variant.name.startsWith("official")) {
                    pathname = "Weee! Official " + suffix;
                } else if (variant.name.startsWith("latino")) {
                    pathname = "MasGusto" + suffix;
                } else {
                    // Notice: Setting outputDirectory will cause create${variant}ApkListingFileRedirect failed
                    // pathname = "Weee! [Dev] " + suffix;
                    pathname = null
                }
                if (pathname != null) {
                    def outputDirectory = new File(project.rootDir.absolutePath + "/app/apk")
                    variant.getPackageApplicationProvider().get().outputDirectory = outputDirectory
                    outputFileName = new File(pathname)
                }
            }
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    flavorDimensions = ["default"]

    productFlavors {

        official { //weee!
            isDefault = true

            resValue "string", "app_sign", "official"

            resourceConfigurations += [
                    'en',
                    'zh', 'zh-rCN', 'zh-rTW',
                    'ko', 'ko-rKR',
                    'ja', 'ja-rJP',
                    'vi', 'vi-rVN'
            ]
        }

        official_hw { //special weee! for huawei
            initWith official
            resValue "string", "app_sign", "official_hw"
        }

        latino { //latino
            applicationId "com.sayweee.latino"
            resValue "string", "app_sign", "latino"

            resourceConfigurations += [
                    'en',
                    'es', 'es-rES', 'es-rUS',
                    'pt', 'pt-rPT', 'pt-rBR'
            ]
        }
    }

    sourceSets {
        official {
            java.srcDirs += 'src/weee/java'
            res.srcDirs += 'src/weee/res'
        }

        official_hw {
            java.srcDirs += 'src/weee/java'
            res.srcDirs += 'src/weee/res'
        }
    }

    packagingOptions {
        resources {
            excludes += [
                    'META-INF/DEPENDENCIES',
                    'META-INF/LICENSE',
                    'META-INF/NOTICE',
                    'META-INF/MANIFEST.MF',
                    'META-INF/*.kotlin_module'
            ]
        }
    }

    bundle {
        language {
            enableSplit false
        }
        density {
            enableSplit true
        }
        abi {
            enableSplit true
        }
    }

    buildFeatures {
        viewBinding = true
        buildConfig = true
        aidl = true
    }

    lint {
        abortOnError false
        checkReleaseBuilds false
    }

}

// Workaround for Setting outputDirectory will cause create${variant}ApkListingFileRedirect failed
tasks.configureEach { task ->
    // disable task when release
    if (task.name.contains("ReleaseApkListingFileRedirect")) {
        task.enabled = false
    }
}


tasks.withType(JavaCompile).configureEach {
    /* Workaround for https://github.com/bumptech/glide/issues/5003.
    options.fork = true
    options.forkOptions.jvmArgs += [
            "--add-opens=jdk.compiler/com.sun.tools.javac.code=ALL-UNNAMED",
    ]*/
    options.compilerArgs += ['-Xdoclint:none', '-Xlint:none', '-nowarn']
}

apply from: 'depends.gradle'

configurations.all {
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
}

if (ENABLE_MARCO_BENCHMARK == 'true') {
    apply from: 'benchmark.gradle'
}
if (ENABLE_BASELINE_PROFILE == 'true') {
    apply from: 'baselineprofile.gradle'
}

apply from: 'sonar.gradle'