# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile


#---------------------------------基本指令区----------------------------------
# 代码混淆压缩比，在0和7之间，默认为5，一般不需要改
-optimizationpasses 5
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-dontskipnonpubliclibraryclassmembers
-dontpreverify
# 有了verbose这句话，混淆后就会生成映射文件
# 包含有类名->混淆后类名的映射关系
# 然后使用printmapping指定映射文件的名称
-verbose
-printmapping proguardMapping.txt
# 指定混淆时采用的算法，后面的参数是一个过滤器
# 这个过滤器是谷歌推荐的算法，一般不改变
-optimizations !code/simplification/cast,!field/*,!class/merging/*
-keepattributes *Annotation*,InnerClasses
-keepattributes Signature
#抛出异常时保留代码行号，在异常分析中可以方便定位
-keepattributes SourceFile,LineNumberTable
#----------------------------------------------------------------------------

#---------------------------------默认保留区---------------------------------
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends android.app.backup.BackupAgentHelper
-keep public class * extends android.preference.Preference
-keep public class * extends android.view.View
-keep public class * extends androidx.*.**
-keep public class * extends android.*.**
-keep class android.*.** {*;}
-keep class androidx.*.** {*;}
-keep class com.google.*.** {*;}
-keep interface android.*.** {*;}
-keep interface androidx.*.** {*;}
-dontwarn com.google.android.material.**
-dontnote com.google.android.material.**
-dontwarn androidx.**

-keepclassmembers class * extends android.app.Activity{
    public void *(android.view.View);
}
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}
-keep public class * extends android.view.View{
    *** get*();
    void set*(***);
    public <init>(android.content.Context);
    public <init>(android.content.Context, java.lang.Boolean);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
}
-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

-keep class * implements android.os.Parcelable {
  public static final android.os.Parcelable.Creator *;
  public static final ** CREATOR;
}

-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}
#native方法
-keepclasseswithmembernames class * {
    native <methods>;
}
#R文件
-keep class **.R$* {
    *;
}
#prevent missing search record history
-keep class com.sayweee.weee.module.search.service.SearchRecordViewModel{ *; }
#----------------------------------------------------------------------------

#避免Log打印输出
-assumenosideeffects class android.util.Log {
    public static *** v(...);
    public static *** d(...);
    public static *** i(...);
    public static *** w(...);
    public static *** e(...);
    public static *** wtf(...);
    public static *** println(...);
}

#---------------------------------webview------------------------------------
-keepclassmembers class android.webkit.WebView {
   public *;
}
-keepclassmembers class * extends android.webkit.WebViewClient {
    public void *(android.webkit.WebView, java.lang.String, android.graphics.Bitmap);
    public boolean *(android.webkit.WebView, java.lang.String);
}
-keepclassmembers class * extends android.webkit.WebViewClient {
    public void *(android.webkit.WebView, java.lang.String);
}
#----------------------------------------------------------------------------

#----------------------------------1.Js---------------------------------------


#-------------------------------------------------------------------------

#---------------------------------------------------------------------------------------------------



#---------------------------------2.实体类---------------------------------
-keep class **.*.bean.**{ *; }
-keep class **.*.entry.**{ *; }
-keep class **.*.vo.**{ *; }
-keep class **.*.dao.**{ *; }
-keep class **.*.data.**{ *; }

#-------------------------------------------------------------------------




#---------------------------------3.第三方包-------------------------------

#eventbus 3.0
-keepattributes *Annotation*
-keepclassmembers class * {
    @org.greenrobot.eventbus.Subscribe <methods>;
}
-keep enum org.greenrobot.eventbus.ThreadMode { *; }
-keepclassmembers class * extends org.greenrobot.eventbus.util.ThrowableFailureEvent {
    <init>(java.lang.Throwable);
}

# Glide相关
-dontwarn com.bumptech.glide.**
-keep class com.bumptech.*.** { *; }
-keep interface com.bumptech.*.** { *; }

#bugly
-dontwarn com.tencent.bugly.**
-keep public class com.tencent.*.**{*;}

#butterknife
-keep class butterknife.*.** { *; }
-dontwarn butterknife.internal.**
-keep class **$$ViewBinder { *; }
-keepclasseswithmembernames class * {
    @butterknife.* <fields>;
}
-keepclasseswithmembernames class * {
    @butterknife.* <methods>;
}

# rxjava2
# 无需混淆

#fastjson
-dontwarn com.alibaba.fastjson.**
-keep class com.alibaba.fastjson.* { *; }
-keep class * implements java.io.Serializable { *; }
-keepclassmembers class * {
   public <init> (org.json.JSONObject);
}

# Gson
-keep class com.google.*.** { *; }
-keepattributes Signature
-keep class com.google.gson.reflect.TypeToken { *; }
-keep class * extends com.google.gson.reflect.TypeToken
-keepclassmembers,allowobfuscation class * {
    @com.google.gson.annotations.SerializedName <fields>;
}

# OkHttp3
-dontwarn okhttp3.logging.**
-keep class okhttp3.*.**{*;}
-dontwarn okio.**

# Retrofit
-dontwarn retrofit2.**
-keep class retrofit2.*.** { *; }
# Platform calls Class.forName on types which do not exist on Android to determine platform.
-dontnote retrofit2.Platform
# Platform used when running on RoboVM on iOS. Will not be used at runtime.
-dontnote retrofit2.Platform$IOS$MainThreadExecutor
# Platform used when running on Java 8 VMs. Will not be used at runtime.
-dontwarn retrofit2.Platform$Java8
# Retain generic type information for use by reflection by converters and adapters.

# https://github.com/square/retrofit/blob/trunk/retrofit/src/main/resources/META-INF/proguard/retrofit2.pro
# Retrofit does reflection on generic parameters. InnerClasses is required to use Signature and
# EnclosingMethod is required to use InnerClasses.
-keepattributes Signature, InnerClasses, EnclosingMethod

# Retrofit does reflection on method and parameter annotations.
-keepattributes RuntimeVisibleAnnotations, RuntimeVisibleParameterAnnotations

# Keep annotation default values (e.g., retrofit2.http.Field.encoded).
-keepattributes AnnotationDefault

# Retain service method parameters when optimizing.
-keepclassmembers,allowshrinking,allowobfuscation interface * {
    @retrofit2.http.* <methods>;
}

# Ignore annotation used for build tooling.
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement

# Ignore JSR 305 annotations for embedding nullability information.
-dontwarn javax.annotation.**

# Guarded by a NoClassDefFoundError try/catch and only used when on the classpath.
-dontwarn kotlin.Unit

# Top-level functions that can only be used by Kotlin.
-dontwarn retrofit2.KotlinExtensions
-dontwarn retrofit2.KotlinExtensions$*

# With R8 full mode, it sees no subtypes of Retrofit interfaces since they are created with a Proxy
# and replaces all potential values with null. Explicitly keeping the interfaces prevents this.
-if interface * { @retrofit2.http.* <methods>; }
-keep,allowobfuscation interface <1>

# Keep inherited services.
-if interface * { @retrofit2.http.* <methods>; }
-keep,allowobfuscation interface * extends <1>

# With R8 full mode generic signatures are stripped for classes that are not
# kept. Suspend functions are wrapped in continuations where the type argument
# is used.
-keep,allowobfuscation,allowshrinking class kotlin.coroutines.Continuation

# R8 full mode strips generic signatures from return types if not kept.
-if interface * { @retrofit2.http.* public *** *(...); }
-keep,allowoptimization,allowshrinking,allowobfuscation class <3>

# With R8 full mode generic signatures are stripped for classes that are not kept.
-keep,allowobfuscation,allowshrinking class retrofit2.Response

#友盟
-dontwarn com.umeng.**
-keep class com.umeng.*.** {*;}

#bga
-dontwarn cn.bingoogolapple.**
-keep class cn.bingoogolapple.*.** {*;}


#BaseRecyclerViewAdapterHelper
-keep public class * extends com.chad.library.adapter.base.BaseQuickAdapter
-keep public class * extends com.chad.library.adapter.base.BaseViewHolder
-keepclassmembers  class **$** extends com.chad.library.adapter.base.BaseViewHolder {
     <init>(...);
}
-keepclassmembers class * extends com.chad.library.adapter.base.BaseViewHolder { <init>(...); }

#immersionbar
#no need for version 3.2.2 or later
-keep class com.gyf.immersionbar.* {*;}
-dontwarn com.gyf.immersionbar.**

#dom4j
-dontwarn org.dom4j.**
-keep class org.dom4j.*.** {*;}

#html-parser
-dontwarn org.jaxen.BaseXPath
-dontwarn org.jaxen.DefaultNavigator
-dontwarn org.jaxen.JaxenException
-dontwarn org.jaxen.NamespaceContext
-dontwarn org.jaxen.Navigator
-dontwarn org.jaxen.SimpleVariableContext
-dontwarn org.jaxen.VariableContext
-dontwarn org.jaxen.XPath

#gsyvideoplayer
-keep class com.shuyu.gsyvideoplayer.*.** { *; }
-dontwarn com.shuyu.gsyvideoplayer.*.**
-keep class tv.danmaku.*.** { *; }
-dontwarn tv.danmaku.*.**

#PictureSelector
-keep class com.luck.picture.*.** { *; }

#Ucrop
-dontwarn com.yalantis.*.**
-keep class com.yalantis.*.** { *; }
-keep interface com.yalantis.*.** { *; }

#banner
-dontwarn com.youth.*.**
-keep class com.youth.*.** {*;}

#alipay
-dontwarn com.alipay.*.**
-keep class com.alipay.*.** {*;}

##appsflyer
-dontwarn com.appsflyer.*.**
-keep class com.appsflyer.*.** { *; }

#apache
-dontwarn org.apache.*.**
-dontwarn org.apache.commons.**
-keep class org.apache.*.** {*;}
-keep class org.apache.http.** { *; }

#smartystreets
-dontwarn com.smartystreets.*.**
-keep class com.smartystreets.*.** {*;}

#sentry
-dontwarn io.sentry.*.**
-keep class io.sentry.*.** {*;}

#braintree
-dontwarn com.braintreepayments.*.**
-keep class com.braintreepayments.*.** {*;}

#kakao
-keep class com.kakao.sdk.**.model.* { <fields>; }
-keep class * extends com.google.gson.TypeAdapter

-keep class io.agora.**{*;}
-keep class io.agora.*.** {*;}

-keep class com.amazonaws.ivs.*.**
-keep class com.amazonaws.ivs.*.** {*;}

#ivs-chat
-keep public class com.amazonaws.ivs.chat.messaging.** { *; }
-keep public interface com.amazonaws.ivs.chat.messaging.** { *; }

#citon
-dontwarn sdk.CPaySDK.*.**
-dontwarn com.unionpay.UPPayAssistEx

#line-sdk
-dontwarn com.linecorp.linesdk.*.**

# desugar
-dontwarn com.google.devtools.build.android.desugar.runtime.ThrowableExtension
#waf
-keep class org.bouncycastle.crypto.** { *; }
-keep class com.amazonaws.waf.** { *; }

#klaviyo
-keep class com.klaviyo.analytics.** { *; }
-keep class com.klaviyo.core.** { *; }
-keep class com.klaviyo.*.** { *; }
#-------------------------------------------------------------------------

#preload
-keep class com.sayweee.preload.*.** {*;}

-keep class com.facebook.react.** { *; }
