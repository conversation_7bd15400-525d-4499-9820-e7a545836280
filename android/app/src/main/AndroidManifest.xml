<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="LockedOrientationActivity">

    <uses-sdk tools:overrideLibrary="citcon.sdk" />

    <!--网络权限-->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <!--手机状态-->
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
    <!--硬件权限-->
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <!--文件权限-->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <!--用于进行网络定位-->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <!--用于访问GPS定位-->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <!--后台服务-->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <!-- Camera features - recommended -->
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.camera.flash"
        android:required="false" />

    <queries>
        <package android:name="com.tencent.mm" />
    </queries>

    <queries>
        <package android:name="com.facebook.katana" />
    </queries>

    <queries>
        <package android:name="com.facebook.orca" />
    </queries>

    <queries>
        <package android:name="com.whatsapp" />
    </queries>

    <queries>
        <package android:name="jp.naver.line.android" />
    </queries>

    <queries>
        <package android:name="com.instagram.android" />
    </queries>

    <queries>
        <provider android:authorities="com.facebook.katana.provider.PlatformProvider" />
    </queries>

    <queries>
        <!-- 正式环境 -->
        <package android:name="com.eg.android.AlipayGphone" />
        <!-- 沙箱环境 -->
        <package android:name="com.eg.android.AlipayGphoneRC" />
        <package android:name="hk.alipay.wallet" />
    </queries>

    <queries>
        <package android:name="${applicationId}" />

        <intent>
            <action android:name="android.media.action.IMAGE_CAPTURE" />
        </intent>
        <intent>
            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE" />
        </intent>
    </queries>

    <queries>
        <package android:name="com.kakao.talk" />
    </queries>

    <application
        android:name=".global.App"
        android:allowBackup="false"
        android:fullBackupContent="false"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        tools:replace="android:allowBackup, android:fullBackupContent, android:theme"
        tools:targetApi="q">

        <activity android:name=".react.ReactNavActivity" />

        <!-- 启动页 -->
        <activity
            android:name=".module.launch.SplashActivity"
            android:exported="true"
            android:icon="@mipmap/ic_launcher"
            android:roundIcon="@mipmap/ic_launcher_round"
            android:screenOrientation="portrait"
            android:theme="@style/LaunchTheme">

            <!-- 默认 Launcher -->
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <!-- 支持 *.sayweee.net 或 *.sayweee.com 规则 -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https"/>

                <data android:host="*.sayweee.com" />
                <data android:host="*.sayweee.net" />
            </intent-filter>

            <!-- 支持 appsflyer weeeone.onelink.me/3WRB 规则 -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="weeeone.onelink.me"
                    android:pathPrefix="/3WRB"
                    android:scheme="https" />
            </intent-filter>

            <!-- 支持 sayweee://weee.app -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="weee.app"
                    android:scheme="sayweee" />
            </intent-filter>

            <!-- Add SMS and App DL msg for (web-only users) at Order Confirmation -->
            <!-- 支持 http://syw.bz/k -->
            <!-- 支持 https://syw.bz/k -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https"/>
                <data android:scheme="http"/>
                <data android:host="syw.bz"/>
                <data android:pathPrefix="/k"/>
            </intent-filter>

        </activity>

        <!-- 启动页 alias1 -->
        <activity-alias
            android:name="com.sayweee.weee.IconPlaceholder"
            android:enabled="false"
            android:exported="true"
            android:icon="@mipmap/icon_2025_new_year"
            android:roundIcon="@mipmap/icon_2025_new_year_round"
            android:targetActivity=".module.launch.SplashActivity">

            <!-- 默认 Launcher -->
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <!-- 支持 *.sayweee.net 或 *.sayweee.com 规则 -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https"/>

                <data android:host="*.sayweee.com" />
                <data android:host="*.sayweee.net" />
            </intent-filter>

            <!-- 支持 appsflyer weeeone.onelink.me/3WRB 规则 -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="weeeone.onelink.me"
                    android:pathPrefix="/3WRB"
                    android:scheme="https" />
            </intent-filter>

            <!-- 支持 sayweee://weee.app -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="weee.app"
                    android:scheme="sayweee" />
            </intent-filter>

            <!-- Add SMS and App DL msg for (web-only users) at Order Confirmation -->
            <!-- 支持 http://syw.bz/k -->
            <!-- 支持 https://syw.bz/k -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https"/>
                <data android:scheme="http"/>
                <data android:host="syw.bz"/>
                <data android:pathPrefix="/k"/>
            </intent-filter>

        </activity-alias>

        <!-- 启动页 alias2 -->
        <activity-alias
            android:name="com.sayweee.weee.IconPlaceholder2"
            android:enabled="false"
            android:exported="true"
            android:icon="@mipmap/icon_2024_holiday_season"
            android:roundIcon="@mipmap/icon_2024_holiday_season_round"
            android:targetActivity=".module.launch.SplashActivity">

            <!-- 默认 Launcher -->
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <!-- 支持 *.sayweee.net 或 *.sayweee.com 规则 -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https"/>

                <data android:host="*.sayweee.com" />
                <data android:host="*.sayweee.net" />
            </intent-filter>

            <!-- 支持 appsflyer weeeone.onelink.me/3WRB 规则 -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="weeeone.onelink.me"
                    android:pathPrefix="/3WRB"
                    android:scheme="https" />
            </intent-filter>

            <!-- 支持 sayweee://weee.app -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="weee.app"
                    android:scheme="sayweee" />
            </intent-filter>

            <!-- Add SMS and App DL msg for (web-only users) at Order Confirmation -->
            <!-- 支持 http://syw.bz/k -->
            <!-- 支持 https://syw.bz/k -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https"/>
                <data android:scheme="http"/>
                <data android:host="syw.bz"/>
                <data android:pathPrefix="/k"/>
            </intent-filter>

        </activity-alias>

        <!--system maintenance status-->
        <activity
            android:name=".module.launch.SystemStatusActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <!--main-->
        <activity
            android:name=".module.MainActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode|locale|layoutDirection"
            android:label="@string/app_name"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!--cart-->
        <activity
            android:name=".module.cart.CartActivity"
            android:label="@string/app_name"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/TransTheme" />
        <activity
            android:name=".module.cart.RecommendItemsActivity"
            android:screenOrientation="portrait" />
        <!--WebView页面-->
        <activity
            android:name=".module.web.WebViewActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".service.experiment.ExperimentRouteActivity"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".module.account.LoginRouteActivity"
            android:screenOrientation="portrait" />

        <!--new 商品详情-->
        <activity
            android:name=".module.product.NewProductDetailActivity"
            android:screenOrientation="portrait" />
        <!--商品图片-->
        <activity
            android:name=".module.cate.product.ImagePreviewActivity"
            android:screenOrientation="portrait" />
        <!-- 保存图片 -->
        <activity
            android:name=".module.web.ImageWebActivity"
            android:theme="@style/ImageWebTheme" />

        <!--debug-->
        <!--log view-->
        <activity
            android:name=".module.debug.log.LogActivity"
            android:screenOrientation="portrait" />
        <!--debug panel-->
        <activity
            android:name=".module.debug.DebugPanelActivity"
            android:screenOrientation="portrait" />
        <!--debug tools-->
        <activity
            android:name=".module.debug.DebugToolsActivity"
            android:screenOrientation="portrait" />
        <!--app info-->
        <activity
            android:name=".module.debug.AppInfoActivity"
            android:screenOrientation="portrait" />
        <!--ui debug-->
        <activity
            android:name=".module.debug.UiPanelActivity"
            android:screenOrientation="portrait" />
        <!--module debug-->
        <activity
            android:name=".module.debug.ModuleDebugActivity"
            android:screenOrientation="portrait" />
        <!--location test-->
        <activity
            android:name=".module.debug.info.LocationTestActivity"
            android:screenOrientation="portrait" />
        <!--map test-->
        <activity
            android:name=".module.debug.info.MapTestActivity"
            android:screenOrientation="portrait" />
        <!--spread style-->
        <activity
            android:name=".module.debug.ui.SpreadTestActivity"
            android:screenOrientation="portrait" />
        <!--cart operate-->
        <activity
            android:name=".module.debug.ui.CartOpActivity"
            android:screenOrientation="portrait" />
        <!--host toggle-->
        <activity
            android:name=".module.debug.info.HostActivity"
            android:screenOrientation="portrait" />
        <!--web door-->
        <activity
            android:name=".module.debug.WebDoorActivity"
            android:screenOrientation="portrait" />
        <!--ui door-->
        <activity
            android:name=".module.debug.UiDoorActivity"
            android:screenOrientation="portrait" />
        <!--icon test-->
        <activity
            android:name=".module.debug.ui.IconTestActivity"
            android:screenOrientation="portrait" />
        <!--veil test-->
        <activity
            android:name=".module.debug.ui.VeilTestActivity"
            android:screenOrientation="portrait" />
        <!--veil test-->
        <activity
            android:name=".module.debug.ui.VeilMineLoginTestActivity"
            android:screenOrientation="portrait" />
        <!--veil test-->
        <activity
            android:name=".module.debug.ui.VeilMineNotLoginTestActivity"
            android:screenOrientation="portrait" />
        <!--image search test-->
        <activity
            android:name=".module.debug.ui.ImageSearchTestActivity"
            android:screenOrientation="portrait" />
        <!--dynamic config test-->
        <activity
            android:name=".module.debug.ui.DynamicConfigTestActivity"
            android:screenOrientation="portrait" />

        <!--launch-->
        <!--引导页-->
        <activity
            android:name=".module.launch.GuideActivity"
            android:screenOrientation="portrait"
            android:theme="@style/GuideTheme" />
        <!--引导页-->
        <activity
            android:name=".module.launch.NewGuideActivity"
            android:screenOrientation="portrait"
            android:theme="@style/GuideTheme" />
        <activity
            android:name=".module.account.LoginMethodActivity"
            android:screenOrientation="portrait"
            android:theme="@style/TransTheme" />
        <activity
            android:name=".module.account.PasswordActivity"
            android:screenOrientation="portrait"
            android:theme="@style/TransTheme" />
        <!--输入Zip Code-->
        <activity
            android:name=".module.launch.ZipCodeInputActivity"
            android:screenOrientation="portrait"
            android:theme="@style/TransTheme"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <!--服务订阅页-->
        <activity
            android:name=".module.launch.ServiceSubscribeActivity"
            android:screenOrientation="portrait"
            android:theme="@style/TransTheme" />
        <!--订阅结果页-->
        <activity
            android:name=".module.launch.SubscribeResultActivity"
            android:screenOrientation="portrait"
            android:theme="@style/TransTheme" />

        <!--account-->
        <!--登陆-->
        <activity
            android:name=".module.account.LoginPanelActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/TransTheme" />
        <!--登陆-->
        <activity
            android:name=".module.account.LoginActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/TransTheme" />
        <!--GRO-2788登陆-->
        <activity
            android:name=".module.account.LoginContainerActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/TransTheme" />
        <!--授权登陆-->
        <activity
            android:name=".module.account.OAuthPanelActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/TransTheme" />
        <!--注册-->
        <activity
            android:name=".module.account.RegisterPanelActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/TransTheme" />
        <!--绑定账号-->
        <activity
            android:name=".module.account.AccountBindActivity"
            android:screenOrientation="portrait"
            android:theme="@style/TransTheme" />
        <!--学生权益-->
        <activity
            android:name=".module.account.StudentRuleActivity"
            android:screenOrientation="portrait"
            android:theme="@style/TransTheme" />
        <!--学生手机号验证-->
        <activity
            android:name=".module.account.StudentPhoneActivity"
            android:screenOrientation="portrait"
            android:theme="@style/TransTheme" />
        <!--免密登錄-->
        <activity
            android:name=".module.account.MagicSignInRuleActivity"
            android:screenOrientation="portrait"
            android:theme="@style/TransTheme" />
        <!--忘记密码-->
        <activity
            android:name=".module.account.ForgetPasswordActivity"
            android:screenOrientation="portrait"
            android:theme="@style/TransTheme" />
        <!--输入验证码-->
        <activity
            android:name=".module.account.ConfirmCodeActivity"
            android:screenOrientation="portrait"
            android:theme="@style/TransTheme" />
        <!--邮箱校验码-->
        <activity
            android:name=".module.account.AccountVerifyActivity"
            android:screenOrientation="portrait"
            android:theme="@style/TransTheme" />
        <activity
            android:name=".module.account.NewAccountVerifyActivity"
            android:screenOrientation="portrait"
            android:theme="@style/TransTheme" />
        <!--学生邮箱校验码-->
        <activity
            android:name=".module.account.StudentVerifyActivity"
            android:screenOrientation="portrait"
            android:theme="@style/TransTheme" />
        <!--重设密码-->
        <activity
            android:name=".module.account.ResetPasswordActivity"
            android:screenOrientation="portrait"
            android:theme="@style/TransTheme" />

        <!--homepage-->
        <!--首页修改日期date-->
        <activity
            android:name=".module.home.date.DateActivity"
            android:screenOrientation="portrait" />
        <!--首页修改zipcode-->
        <activity
            android:name=".module.home.zipcode.LocationActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name=".module.home.zipcode.AddressManageActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name=".module.home.zipcode.DeliverInfoEnkiActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name=".module.home.zipcode.SearchAddressEnkiActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <!--zipcode页面新增地址-->
        <!--暂时用web页面-->
        <!--搜索-->
        <activity
            android:name=".module.search.SearchPanelActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name=".module.search.StubSearchPanelActivity"
            android:launchMode="standard"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <!--拍照购-->
        <activity
            android:name=".module.search.RecognizeImageActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <!--搜索结果为空， 登记需求-->
        <activity
            android:name=".module.search.RequestProductActivity"
            android:screenOrientation="portrait" />
        <!--扫描-->
        <activity
            android:name=".module.web.ScanActivity"
            android:screenOrientation="portrait" />
        <!--扫描-->
        <activity
            android:name=".module.search.ScanCodeActivity"
            android:screenOrientation="portrait" />
        <!--连续扫描-->
        <activity
            android:name=".module.web.ScanStayActivity"
            android:screenOrientation="portrait" />
        <!--Cuisine页面-->
        <activity
            android:name=".module.cate.CateActivity"
            android:screenOrientation="portrait" />
        <!--AI助手页面-->
        <activity
            android:name=".module.search.ShoppingAssistantActivity"
            android:screenOrientation="portrait" />
        <!--Cate Window页面-->
        <!--global+-->
        <activity
            android:name=".module.mkpl.GlobalActivity"
            android:screenOrientation="portrait" />

        <!-- global plus -->
        <activity
            android:name=".module.mkpl.home.GlobalPlusActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".module.mkpl.fbw.FbwLandingActivity"
            android:screenOrientation="portrait" />


        <!--seller info-->
        <activity
            android:name=".module.seller.SellerInfoActivity"
            android:screenOrientation="portrait" />
        <!--商品 brand list 页面-->
        <activity
            android:name=".module.cate.product.BrandActivity"
            android:screenOrientation="portrait" />
        <!--seller home-->
        <activity
            android:name=".module.seller.SellerActivity"
            android:screenOrientation="portrait" />

        <!-- ******************************************************************************* -->
        <!--结算-->
        <!--upSell-->
        <activity
            android:name=".module.checkout.UpSellActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.checkout.SectionUpsellActivity"
            android:screenOrientation="portrait" />
        <!--alcohol agreement-->
        <activity
            android:name=".module.checkout.AlcoholAgreementActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.checkout.SectionAlcoholAgreementActivity"
            android:screenOrientation="portrait" />
        <!--checkout-->
        <activity
            android:name=".module.checkout.CheckOutActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <!--checkout-->
        <activity
            android:name=".module.checkout.CheckOutSectionActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <!--checkout add email-->
        <activity
            android:name=".module.checkout.CheckOutAddEmailActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <!--order products-->
        <activity
            android:name=".module.checkout.OrderProductsActivity"
            android:screenOrientation="portrait" />
        <!--order note-->
        <activity
            android:name=".module.checkout.OrderNoteActivity"
            android:screenOrientation="portrait" />
        <!--delivery info-->
        <activity
            android:name=".module.checkout.DeliveryInfoActivity"
            android:screenOrientation="portrait" />
        <!--delivery address-->
        <activity
            android:name=".module.checkout.DeliveryAddressPickerActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.checkout.DeliveryAddressEditActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name=".module.checkout.PickupAddressActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name=".module.checkout.GoogleAddressActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.home.zipcode.AddressAddManuallyActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.checkout.AddressMapPinActivity"
            android:screenOrientation="portrait" />
        <!--pay type-->
        <activity
            android:name=".module.checkout.PaymentMethodActivity"
            android:screenOrientation="portrait" />
        <!--add card-->
        <activity
            android:name=".module.checkout.CreditCardAddActivity"
            android:screenOrientation="portrait" />
        <!--add card-->
        <activity
            android:name=".module.checkout.BraintreeCreditCardAddActivity"
            android:screenOrientation="portrait" />
        <!-- PayPal Pay -->
        <activity
            android:name=".module.checkout.PayPalPaySetupActivity"
            android:excludeFromRecents="true"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/TransparentCompat">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="${applicationId}.braintree-paypal" />
            </intent-filter>
        </activity>
        <!-- Venmo Pay -->
        <activity
            android:name=".module.checkout.VenmoPaySetupActivity"
            android:excludeFromRecents="true"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/TransparentCompat" />
        <!-- Cash App Pay -->
        <activity
            android:name=".module.checkout.CashAppPaySetupActivity"
            android:excludeFromRecents="true"
            android:exported="true"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/TransparentCompat">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="${applicationId}.cpay.sdk"
                    android:scheme="citcon" />
            </intent-filter>

        </activity>
        <!--coupon-->
        <activity
            android:name=".module.checkout.CouponActivity"
            android:screenOrientation="portrait" />
        <!--store option-->
        <activity
            android:name=".module.launch.StoreOptionActivity"
            android:screenOrientation="portrait" />
        <!--post detail-->
        <activity
            android:name=".module.post.detail.ReviewDetailActivity"
            android:screenOrientation="portrait" />
        <!--edit post-->
        <activity
            android:name=".module.post.edit.PostEditorActivity"
            android:screenOrientation="portrait" />
        <!--suggest translation-->
        <activity
            android:name=".module.post.edit.SuggestTranslationsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.post.edit.SelectCoverActivity"

            android:screenOrientation="portrait" />
        <!--preview video-->
        <activity
            android:name=".module.post.edit.PreviewVideoActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:screenOrientation="portrait" />
        <!-- Add hashtags -->
        <activity
            android:name=".module.post.edit.AddHashtagsActivity"
            android:screenOrientation="portrait" />

        <!--ready to checkout-->
        <activity
            android:name=".module.checkout.Ready2CheckoutActivity"
            android:screenOrientation="portrait" />

        <!--post搜索界面-->
        <activity
            android:name=".module.post.PostSearchActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <!--more post-->
        <activity
            android:name=".module.post.PostListActivity"
            android:screenOrientation="portrait" />
        <!--comment attach products and posts-->
        <activity
            android:name=".module.post.product.PostAttachActivity"
            android:screenOrientation="portrait"
            android:theme="@style/TransTheme" />
        <!--video post-->
        <activity
            android:name=".module.post.PostVideoDetailActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:screenOrientation="portrait" />
        <!--profile-->
        <activity
            android:name=".module.post.profile.ProfileActivity"
            android:screenOrientation="portrait" />
        <!--profile follow-->
        <activity
            android:name=".module.post.profile.ProfileFollowActivity"
            android:screenOrientation="portrait" />
        <!--thematic-->
        <activity
            android:name=".module.thematic.ThematicActivity"
            android:screenOrientation="portrait" />

        <!--To review host page -->
        <activity
            android:name=".module.post.review.ToReviewHostActivity"
            android:screenOrientation="portrait" />

        <!-- Review edit page -->
        <activity
            android:name=".module.post.review.ReviewEditActivity"
            android:screenOrientation="portrait" />

        <!-- Review submitted page -->
        <activity
            android:name=".module.post.review.ReviewSubmittedActivity"
            android:screenOrientation="portrait" />
        <!-- explore for seller review post videos -->
        <activity
            android:name=".module.post.explore.PostExploreActivity"
            android:screenOrientation="portrait" />

        <!-- ******************************************************************************* -->
        <!--消息中心-->
        <!--message portal-->
        <activity
            android:name=".module.message.MessagePortalActivity"
            android:screenOrientation="portrait" />
        <!--message portal-->
        <activity
            android:name=".module.message.MessageCenterActivity"
            android:screenOrientation="portrait" />
        <!--activity portal-->
        <activity
            android:name=".module.message.ActivityCenterActivity"
            android:screenOrientation="portrait" />
        <!--activity portal-->
        <activity
            android:name=".module.message.NotificationCenterActivity"
            android:screenOrientation="portrait" />
        <!-- ******************************************************************************* -->
        <!--cms collection-->
        <activity
            android:name=".module.collection.CollectionActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.collection.CollectionAutoActivity"
            android:screenOrientation="portrait" />
        <!-- ******************************************************************************* -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!--kakao-->
        <activity
            android:name="com.kakao.sdk.auth.AuthCodeHandlerActivity"
            android:exported="true">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="oauth"
                    android:scheme="kakaof6f3fad3cea4b76938ddf7b763787077" />
            </intent-filter>
        </activity>

        <!--facebook-->
        <activity
            android:name="com.facebook.FacebookActivity"
            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            tools:replace="android:theme" />
        <activity
            android:name="com.facebook.CustomTabActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="@string/fb_protocol_scheme" />
            </intent-filter>
        </activity>

        <provider
            android:name="com.facebook.FacebookContentProvider"
            android:authorities="@string/fb_content_provider_authorities"
            android:exported="true" />

        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/fb_app_id" />
        <meta-data
            android:name="com.facebook.sdk.ClientToken"
            android:value="@string/fb_client_token" />

        <meta-data
            android:name="io.fabric.ApiKey"
            android:value="a5ef6de944b99a9f51f6b42bbc5ebcdc25c9815e" />

        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="AIzaSyDWB50kJzFfWfB5GzPIvOGSGeq0VrBJ5FI" />
        <!--airship-->
        <!--Autopilot will automatically call takeOff-->
        <meta-data
            android:name="com.urbanairship.autopilot"
            android:value="com.sayweee.weee.service.MyAutopilot" />

        <!--wechat-->
        <activity
            android:name=".wxapi.WXEntryActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:exported="true"
            android:launchMode="singleTask"
            android:taskAffinity="${applicationId}"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <activity-alias
            android:name=".wxapi.WXPayEntryActivity"
            android:exported="true"
            android:targetActivity="sdk.PaymentActivity" />



        <!-- order -->
        <activity
            android:name=".module.order.list.OrderListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.order.list.OrderSearchActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.order.reorder.ReorderActivity"
            android:screenOrientation="portrait" />

        <service
            android:name=".service.MyFirebaseMessagingService"
            android:exported="true"
            android:foregroundServiceType=""><!-- Not a foreground service -->
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <!--appsflyer-->
        <receiver
            android:name="com.appsflyer.SingleInstallBroadcastReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="com.android.vending.INSTALL_REFERRER" />
            </intent-filter>
        </receiver>

        <!--inner crash-->
        <meta-data
            android:name="REPORT_KEY_BUGLY"
            android:value="d919528748" />

        <!--firebase performance-->
        <meta-data
            android:name="firebase_performance_logcat_enabled"
            android:value="true" />
        <!--
        &lt;!&ndash;gps&ndash;&gt;
        <service
            android:name=".module.web.gps.DownloadService"
            android:exported="false"
            android:foregroundServiceType="location" />
        <service
            android:name=".module.web.gps.GuardService"
            android:exported="false"
            android:foregroundServiceType="location" />
        <service
            android:name=".module.web.gps.StepService"
            android:exported="false"
            android:foregroundServiceType="location" />
        <service
            android:name=".module.web.gps.ScheduleService"
            android:enabled="true"
            android:exported="true"
            android:foregroundServiceType="location"
            android:permission="android.permission.BIND_JOB_SERVICE" />-->

        <meta-data
            android:name="io.sentry.dsn"
            android:value="https://<EMAIL>/6604819" />
        <!-- Set tracesSampleRate to 1.0 to capture 100% of transactions for performance monitoring.
           We recommend adjusting this value in production. -->
        <meta-data
            android:name="io.sentry.traces.sample-rate"
            android:value="1.0" />
        <!-- Enable user interaction tracing to capture transactions for various UI events (such as clicks or scrolls). -->
        <meta-data
            android:name="io.sentry.traces.user-interaction.enable"
            android:value="true" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="${applicationId}.androidx-startup"
            android:exported="false"
            tools:node="merge">
            <meta-data
                android:name="${pkgName}.global.startup.WStoreInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="${pkgName}.global.startup.FacebookInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="${pkgName}.global.startup.PlacesInitializer"
                android:value="androidx.startup" />
        </provider>

    </application>

</manifest>
