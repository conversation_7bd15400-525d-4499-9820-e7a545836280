package com.sayweee.weee.react;


import com.facebook.react.ReactActivity;
import com.facebook.react.ReactActivityDelegate;
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint;
import com.facebook.react.defaults.DefaultReactActivityDelegate;

//
// Created by <PERSON><PERSON> on 14/05/2025.
//
public class ReactNavActivity extends ReactActivity {

    @Override
    protected String getMainComponentName() {
//        return "HelloWorld";
        return "EmbeddedModule";
    }

    private ReactActivityDelegate mReactActivityDelegate;

    @Override
    protected ReactActivityDelegate createReactActivityDelegate() {
        if (mReactActivityDelegate == null) {
            mReactActivityDelegate = new DefaultReactActivityDelegate(
                    this,
                    getMainComponentName(),
                    DefaultNewArchitectureEntryPoint.getFabricEnabled());
        }
        return mReactActivityDelegate;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mReactActivityDelegate != null) {
            mReactActivityDelegate.onDestroy();
            mReactActivityDelegate = null;
        }
    }
}
