import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Button, FlatList, ActivityIndicator, Platform, NativeModules, ToastAndroid } from 'react-native';
import axios from 'axios';
import { UPDATE_CONFIG_URL } from '@env';

// import CodePush from 'react-native-code-push';

import CodePush, {
    ReleaseHistoryInterface,
    UpdateCheckRequest,
} from "@bravemobile/react-native-code-push";

async function releaseHistoryFetcher(
  updateRequest: UpdateCheckRequest,
): Promise<ReleaseHistoryInterface> {

  // Fetch release history for current binary app version.
  // You can implement how to fetch the release history freely. (Refer to the example app if you need a guide)

  // 使用更明显的日志记录方式，确保在 Android 和 iOS 原生日志中可见
  console.log('CODEPUSH_LOG', `App version: ${updateRequest.app_version}`);
  
  // 在 Android 上显示 Toast 消息，这在调试时非常有用
  if (Platform.OS === 'android') {
    try {
      ToastAndroid.showWithGravity(
        `CodePush: App version ${updateRequest.app_version}`,
        ToastAndroid.LONG,
        ToastAndroid.CENTER
      );
    } catch (e) {
      console.warn('Failed to show Android toast', e);
    }
  }

  var updateConfigUrl = UPDATE_CONFIG_URL;
  console.log('CODEPUSH_LOG', `UPDATE_CONFIG_URL: ${updateConfigUrl}`);
  const {data: releaseHistory} = await axios.get<ReleaseHistoryInterface>(
    //`https://your.cdn.com/histories/${platform}/${identifier}/${updateRequest.app_version}.json`,
    `${updateConfigUrl}/releases/${updateRequest.app_version}.json`
  );
  return releaseHistory;
}

const EmbeddedComponent = (props: any) => {
  const { title, dataFromNative } = props;
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);


  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      // Example API endpoint - replace with your actual API
      const response = await fetch('https://jsonplaceholder.typicode.com/posts');
      const jsonData = await response.json();
      setData(jsonData.slice(0, 10)); // Limiting to 10 items for demo
      setLoading(false);
    } catch (err) {
      console.error('Error fetching data:', err);
      setError('Failed to fetch data');
      setLoading(false);
    }
  };

  const renderItem = ({ item }: { item: any  }) => (
    <View style={styles.listItem}>
      <Text style={styles.itemTitle}>{item.title}</Text>
      <Text style={styles.itemBody}>{item.body}</Text>
    </View>
  );

  return (
    <View style={[styles.container, props.style]}>
      <Text style={styles.title}>{title || '更新前背景 白色'}</Text>
      {dataFromNative && (
        <Text style={styles.data}>
          Data: {JSON.stringify(dataFromNative, null, 2)}
        </Text>
      )}

      <Button
        title="Refresh Data"
        onPress={fetchData}
      />

      {loading ? (
        <ActivityIndicator size="large" color="#0000ff" style={styles.loader} />
      ) : error ? (
        <Text style={styles.error}>{error}</Text>
      ) : (
        <FlatList
          data={data}
          renderItem={renderItem}
          keyExtractor={item => item.id.toString()}
          style={styles.list}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 10,
    borderColor: 'blue',
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
    color: 'white',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  data: {
    marginBottom: 8,
  },
  list: {
    width: '100%',
    marginTop: 10,
    maxHeight: 300,
  },
  listItem: {
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
    width: '100%',
  },
  itemTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  itemBody: {
    fontSize: 12,
    color: '#666',
  },
  loader: {
    marginTop: 20,
  },
  error: {
    color: 'red',
    marginTop: 10,
  }
});

export default CodePush({
  checkFrequency: CodePush.CheckFrequency.ON_APP_START,
  installMode: CodePush.InstallMode.ON_NEXT_RESUME,
  mandatoryInstallMode: CodePush.InstallMode.ON_NEXT_RESUME,  
  releaseHistoryFetcher: releaseHistoryFetcher,
  onSyncError: (error) => {
    console.log("CodePush sync error:", error);
  }
})(EmbeddedComponent);
