import type {TurboModule} from 'react-native';
import {TurboModuleRegistry} from 'react-native';

// 导入事件发射器类型
import type {EventEmitter} from 'react-native/Libraries/Types/CodegenTypes';

// 定义事件负载的类型
export type CustomEventPayload = {
  eventName: string;
  value: number;
};

export interface Spec extends TurboModule {
  // 定义一个同步方法
  getConstants(): {
    someConstant: string;
  };

  // 定义一个异步方法 (返回 Promise)
  performAsyncTask(param: string): Promise<boolean>;

  // 定义一个可以从原生向 JS 发射的事件
  readonly onCustomEvent: EventEmitter<CustomEventPayload>;
}

export default TurboModuleRegistry.getEnforcing<Spec>('AwesomeModule');
