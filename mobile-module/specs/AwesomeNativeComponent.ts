import type {ViewProps} from 'react-native';
import type {HostComponent} from 'react-native';
import { Int32 } from 'react-native/Libraries/Types/CodegenTypes';

import codegenNativeComponent from 'react-native/Libraries/Utilities/codegenNativeComponent';

// 1. 定义你的自定义属性接口
//    让它继承 ViewProps，这样你的组件就能接收 style, testID 等通用属性
export interface NativeProps extends ViewProps {
  // 在这里定义你自己的属性
  color?: string;
  speed?: Int32;
  // ... 其他你需要的属性
}

// 2. 使用 codegenNativeComponent 创建一个 HostComponent
// 'MyAwesomeComponent' 这个名字是在原生端注册视图时需要用到的 Manager 的名字
// 注意，它会自动在后面加上 "Manager"，所以原生侧的类名应该是 MyAwesomeComponentManager
export default codegenNativeComponent<NativeProps>(
  'AwesomeComponent',
) as HostComponent<NativeProps>;